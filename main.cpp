/**
 * @file main.cpp
 * @brief MyAvPullStream类的测试主程序
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */

#include <iostream>
#include <string>
#include <signal.h>
#include <unistd.h>
#include "MyAvPullStream.h"

// 全局变量，用于信号处理
static bool g_running = true;
static MyAvPullStream* g_pullStream = nullptr;

/**
 * @brief 信号处理函数，用于优雅退出
 * @param sig 信号编号
 */
void signalHandler(int sig) {
    std::cout << "[INFO] Received signal " << sig << ", shutting down gracefully..." << std::endl;
    g_running = false;

    if (g_pullStream) {
        g_pullStream->stop();
    }
}

/**
 * @brief 打印使用说明
 * @param programName 程序名称
 */
void printUsage(const char* programName) {
    std::cout << "Usage: " << programName << " [stream_url]" << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  " << programName << " rtsp://admin:password@192.168.1.100:554/stream1" << std::endl;
    std::cout << "  " << programName << " rtmp://live.example.com/live/stream" << std::endl;
    std::cout << "  " << programName << " http://example.com/live.m3u8" << std::endl;
    std::cout << "  " << programName << " test.mp4" << std::endl;
    std::cout << std::endl;
    std::cout << "If no URL is provided, a default test stream will be used." << std::endl;
}

/**
 * @brief 主函数
 * @param argc 参数个数
 * @param argv 参数数组
 * @return 程序退出码
 */
int main(int argc, char* argv[]) {
    // 程序开始
    std::cout << "[INFO] === MyAvPullStream Test Program Started ===" << std::endl;

    // 设置信号处理
    signal(SIGINT, signalHandler);   // Ctrl+C
    signal(SIGTERM, signalHandler);  // 终止信号
    
    // 解析命令行参数
    std::string inputFile;
    if (argc > 1) {
        inputFile = argv[1];
        if (inputFile == "-h" || inputFile == "--help") {
            printUsage(argv[0]);
            return 0;
        }
    } else {
        // 默认测试文件（请确保该文件存在）
        inputFile = "1.mp4";
        std::cout << "[INFO] No input file provided, using default test file: " << inputFile << std::endl;
        std::cout << "[INFO] Please make sure the test file exists or provide a valid video file path." << std::endl;
    }

    std::cout << "[INFO] Input file: " << inputFile << std::endl;
    
    try {
        // 创建拉流对象
        g_pullStream = new MyAvPullStream();

        // 设置输入文件路径
        std::cout << "[INFO] Setting input file: " << inputFile << std::endl;
        g_pullStream->setInputFile(inputFile);

        // 启动拉流
        std::cout << "[INFO] Starting video processing..." << std::endl;
        g_pullStream->start();
        
        // 主循环 - 监控处理状态
        int statusCheckCount = 0;
        while (g_running) {
            // 每5秒打印一次状态
            if (statusCheckCount % 50 == 0) {
                bool pullState = g_pullStream->get_pull_state();
                std::cout << "[INFO] Video processing status: " << (pullState ? "RUNNING" : "STOPPED")
                          << ", connect count: " << g_pullStream->mConnectCount
                          << ", video info: " << g_pullStream->img_w << "x" << g_pullStream->img_h
                          << "@" << g_pullStream->fps << "fps" << std::endl;

                // 如果处理已停止且不是用户主动停止，则退出
                if (!pullState && g_running) {
                    std::cerr << "[ERROR] Video processing stopped unexpectedly, exiting..." << std::endl;
                    break;
                }
            }

            // 休眠100毫秒
            usleep(100000);
            statusCheckCount++;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception occurred: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        std::cerr << "[ERROR] Unknown exception occurred" << std::endl;
        return -1;
    }

    // 清理资源
    if (g_pullStream) {
        std::cout << "[INFO] Stopping video processing..." << std::endl;
        g_pullStream->stop();

        std::cout << "[INFO] Deleting video processing instance..." << std::endl;
        delete g_pullStream;
        g_pullStream = nullptr;
    }

    std::cout << "[INFO] === MyAvPullStream Test Program Finished ===" << std::endl;
    return 0;
}
