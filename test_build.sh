#!/bin/bash

# 测试编译脚本
# Test build script for MyAvPullStream project

echo "=== MyAvPullStream 编译测试 ==="
echo "Testing MyAvPullStream build..."

# 检查必要的工具
echo "检查编译工具..."
echo "Checking build tools..."

if ! command -v g++ &> /dev/null; then
    echo "错误: g++ 未安装"
    echo "Error: g++ not found"
    exit 1
fi

if ! command -v make &> /dev/null; then
    echo "错误: make 未安装"
    echo "Error: make not found"
    exit 1
fi

echo "✓ 编译工具检查通过"
echo "✓ Build tools check passed"

# 检查FFmpeg库
echo "检查FFmpeg库..."
echo "Checking FFmpeg libraries..."

if ! pkg-config --exists libavformat; then
    echo "警告: libavformat 未找到，尝试继续编译..."
    echo "Warning: libavformat not found, trying to continue..."
fi

if ! pkg-config --exists libavcodec; then
    echo "警告: libavcodec 未找到，尝试继续编译..."
    echo "Warning: libavcodec not found, trying to continue..."
fi

# 检查OpenCV库
echo "检查OpenCV库..."
echo "Checking OpenCV libraries..."

if ! pkg-config --exists opencv4; then
    if ! pkg-config --exists opencv; then
        echo "警告: OpenCV 未找到，尝试继续编译..."
        echo "Warning: OpenCV not found, trying to continue..."
    fi
fi

echo "✓ 库检查完成"
echo "✓ Library check completed"

# 清理之前的编译
echo "清理之前的编译文件..."
echo "Cleaning previous build..."
make clean 2>/dev/null

# 尝试编译
echo "开始编译..."
echo "Starting build..."

if make; then
    echo "✓ 编译成功！"
    echo "✓ Build successful!"
    
    # 检查可执行文件
    if [ -f "video_processor" ]; then
        echo "✓ 可执行文件已生成: video_processor"
        echo "✓ Executable generated: video_processor"
        
        # 显示文件信息
        ls -la video_processor
        
        echo ""
        echo "使用方法:"
        echo "Usage:"
        echo "  ./video_processor <video_file>"
        echo "  ./video_processor --help"
        echo ""
        echo "示例:"
        echo "Examples:"
        echo "  ./video_processor test.mp4"
        echo "  ./video_processor /path/to/video.avi"
        
    else
        echo "✗ 编译成功但未找到可执行文件"
        echo "✗ Build successful but executable not found"
        exit 1
    fi
else
    echo "✗ 编译失败"
    echo "✗ Build failed"
    echo ""
    echo "可能的解决方案:"
    echo "Possible solutions:"
    echo "1. 安装依赖库: make install-deps"
    echo "2. 检查库路径是否正确"
    echo "3. 查看编译错误信息"
    exit 1
fi

echo ""
echo "=== 编译测试完成 ==="
echo "=== Build test completed ==="
