# Makefile for MyAvPullStream project
# 编译MyAvPullStream项目的Makefile

# 编译器设置
CXX = g++
CXXFLAGS = -std=c++11 -Wall -O2 -g

# 包含路径
INCLUDES = -I/usr/include/opencv4 \
           -I/usr/local/include \
           -I.

# 库路径
LIBDIRS = -L/usr/local/lib \
          -L/usr/lib/x86_64-linux-gnu

# 链接库
LIBS = -lavformat \
       -lavcodec \
       -lavutil \
       -lswscale \
       -lswresample \
       -lopencv_core \
       -lopencv_imgproc \
       -lopencv_imgcodecs \
       -lopencv_highgui \
       -lpthread

# 目标文件
TARGET = video_processor

# 源文件
SOURCES = main.cpp \
          MyAvPullStream.cpp

# 默认目标
all: $(TARGET)

# 直接编译生成可执行文件（不生成.o文件）
$(TARGET): $(SOURCES)
	@echo "Compiling and linking $(TARGET)..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) $(SOURCES) $(LIBDIRS) $(LIBS) -o $(TARGET)
	@echo "Build completed successfully!"

# 清理
clean:
	@echo "Cleaning up..."
	rm -f $(TARGET) *.o
	@echo "Clean completed!"

# 安装依赖（Ubuntu/Debian）
install-deps:
	@echo "Installing dependencies..."
	sudo apt-get update
	sudo apt-get install -y \
		build-essential \
		cmake \
		pkg-config \
		libavformat-dev \
		libavcodec-dev \
		libavutil-dev \
		libswscale-dev \
		libswresample-dev \
		libopencv-dev \
		libopencv-contrib-dev

# 运行程序
run: $(TARGET)
	@echo "Running $(TARGET)..."
	./$(TARGET)

# 使用测试视频运行
run-test: $(TARGET)
	@echo "Running $(TARGET) with test video..."
	@if [ -f "test.mp4" ]; then \
		./$(TARGET) test.mp4; \
	else \
		echo "test.mp4 not found. Please provide a video file."; \
		echo "Usage: ./$(TARGET) <video_file>"; \
	fi

# 帮助信息
help:
	@echo "Available targets:"
	@echo "  all          - Build the project"
	@echo "  clean        - Remove object files and executable"
	@echo "  install-deps - Install required dependencies (Ubuntu/Debian)"
	@echo "  run          - Build and run the program"
	@echo "  run-test     - Build and run with test.mp4"
	@echo "  help         - Show this help message"

# 声明伪目标
.PHONY: all clean install-deps run run-test help
