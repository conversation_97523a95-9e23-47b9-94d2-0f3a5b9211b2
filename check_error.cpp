#include <iostream>
#include <cstdio>

extern "C" {
#include <libavutil/error.h>
}

int main() {
    int error_code = -1094995529;
    char error_buf[AV_ERROR_MAX_STRING_SIZE];
    
    // 将错误码转换为字符串
    av_strerror(error_code, error_buf, AV_ERROR_MAX_STRING_SIZE);
    
    std::cout << "Error code: " << error_code << std::endl;
    std::cout << "Error message: " << error_buf << std::endl;
    
    // 检查常见的错误码
    std::cout << "\nCommon FFmpeg error codes:" << std::endl;
    std::cout << "AVERROR_INVALIDDATA: " << AVERROR_INVALIDDATA << std::endl;
    std::cout << "AVERROR(EAGAIN): " << AVERROR(EAGAIN) << std::endl;
    std::cout << "AVERROR_EOF: " << AVERROR_EOF << std::endl;
    std::cout << "AVERROR(ENOMEM): " << AVERROR(ENOMEM) << std::endl;
    
    // 检查是否匹配
    if (error_code == AVERROR_INVALIDDATA) {
        std::cout << "\nThis is AVERROR_INVALIDDATA - Invalid data found when processing input" << std::endl;
    }
    
    return 0;
}
