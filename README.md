# MyAvPullStream 视频处理项目

这是一个基于FFmpeg和OpenCV的视频文件处理项目，支持本地视频文件的解码和处理。

## 功能特性

- ✅ 支持多种视频格式（MP4、AVI、MKV等）
- ✅ 硬件加速解码（NVIDIA CUVID）
- ✅ 多线程处理架构
- ✅ 实时视频帧解码
- ✅ YUV420P到BGR24格式转换
- ✅ OpenCV图像处理集成
- ✅ 优雅的错误处理和资源管理
- ✅ 详细的中文注释

## 系统要求

### 依赖库
- FFmpeg (libavformat, libavcodec, libavutil, libswscale, libswresample)
- OpenCV 4.x
- C++11 编译器
- pthread

### 支持的系统
- Ubuntu 18.04+
- Debian 10+
- 其他Linux发行版

## 安装依赖

### Ubuntu/Debian
```bash
# 安装编译工具
sudo apt-get update
sudo apt-get install build-essential cmake pkg-config

# 安装FFmpeg开发库
sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev libswscale-dev libswresample-dev

# 安装OpenCV开发库
sudo apt-get install libopencv-dev libopencv-contrib-dev

# 或者使用Makefile自动安装
make install-deps
```

## 编译项目

```bash
# 克隆或下载项目代码
# 进入项目目录

# 编译项目
make

# 或者手动编译
g++ -std=c++11 -Wall -O2 -g \
    -I/usr/include/opencv4 \
    main.cpp MyAvPullStream.cpp Log.cpp \
    -lavformat -lavcodec -lavutil -lswscale -lswresample \
    -lopencv_core -lopencv_imgproc -lopencv_imgcodecs -lopencv_highgui \
    -lpthread \
    -o video_processor
```

## 使用方法

### 基本用法
```bash
# 处理指定的视频文件
./video_processor /path/to/your/video.mp4

# 使用相对路径
./video_processor test.mp4

# 显示帮助信息
./video_processor --help
```

### 支持的视频格式
- MP4 (.mp4)
- AVI (.avi)
- MKV (.mkv)
- MOV (.mov)
- FLV (.flv)
- WebM (.webm)
- 其他FFmpeg支持的格式

### 示例
```bash
# 处理测试视频
make run-test

# 处理自定义视频
./video_processor /home/<USER>/videos/sample.mp4
```

## 项目结构

```
.
├── main.cpp              # 主程序入口
├── MyAvPullStream.h      # 视频处理类头文件
├── MyAvPullStream.cpp    # 视频处理类实现
├── Log.h                 # 日志系统头文件
├── Log.cpp               # 日志系统实现
├── Common.h              # 通用工具函数
├── Makefile              # 编译配置
└── README.md             # 项目说明
```

## 核心类说明

### MyAvPullStream
主要的视频处理类，继承自ThreadedTask，提供以下功能：

- `setInputFile(const std::string& filePath)` - 设置输入视频文件
- `start()` - 启动视频处理线程
- `stop()` - 停止视频处理
- `get_pull_state()` - 获取处理状态

### 线程架构
- **主线程**: 程序控制和状态监控
- **读取线程**: 从视频文件读取数据包
- **解码线程**: 解码视频帧并进行格式转换

## 自定义处理

在 `MyAvPullStream.cpp` 的 `handleDecodeVideo()` 函数中，您可以添加自己的图像处理逻辑：

```cpp
// 创建OpenCV Mat对象
cv::Mat mImage(height, width, CV_8UC3, frame_bgr->data[0], frame_bgr->linesize[0]);

// 在这里添加您的处理逻辑
// 例如：
// - 保存图像: cv::imwrite("frame.jpg", mImage);
// - 显示图像: cv::imshow("Video", mImage); cv::waitKey(1);
// - 图像分析: 进行目标检测、特征提取等
```

## 性能优化

- 支持NVIDIA CUVID硬件解码
- 多线程并行处理
- 内存池管理
- 队列大小自动调节

## 故障排除

### 常见问题

1. **编译错误：找不到头文件**
   ```bash
   # 检查FFmpeg是否正确安装
   pkg-config --cflags libavformat
   
   # 检查OpenCV是否正确安装
   pkg-config --cflags opencv4
   ```

2. **运行时错误：无法打开视频文件**
   - 检查文件路径是否正确
   - 确认文件格式是否支持
   - 检查文件权限

3. **硬件解码失败**
   - 检查是否有NVIDIA显卡
   - 确认CUDA和驱动是否正确安装
   - 程序会自动回退到软件解码

### 调试模式
```bash
# 编译调试版本
make clean
make CXXFLAGS="-std=c++11 -Wall -g -DDEBUG"

# 使用gdb调试
gdb ./video_processor
```

## 许可证

本项目仅供学习和研究使用。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
