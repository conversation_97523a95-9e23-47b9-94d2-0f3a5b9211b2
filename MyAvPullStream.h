#pragma once
#include <condition_variable>
#include <mutex>
#include <queue>
#include <thread>
#include <atomic>
#include <vector>
#include <string>

extern "C"
{
	#include "libavcodec/avcodec.h"
	#include "libavformat/avformat.h"
    #include "libswscale/swscale.h"
    #include <libavutil/imgutils.h>
    #include <libswresample/swresample.h>
}

/**
 * @brief 线程任务基类，提供标准的线程管理接口
 * 
 * 特性：
 * - 统一的线程生命周期管理
 * - 原子操作确保线程安全
 * - 优雅的停止机制
 */
class ThreadedTask 
{
public:
    /**
     * @brief 构造函数，初始化运行状态
     */
    ThreadedTask() : isRunning_(false) {}
    
    /**
     * @brief 虚析构函数，确保正确清理资源
     */
    virtual ~ThreadedTask() {
        stop();
    }

    /**
     * @brief 启动线程任务
     */
    void start()
    {
        if (isRunning_.exchange(true)) {
            return; // 已经在运行
        }
        thread_ = std::thread([this] { this->run(); });
    }

    /**
     * @brief 等待线程结束
     */
    void join() 
    {
        if (thread_.joinable()) {
            thread_.join();
        }
    }

    /**
     * @brief 停止线程任务并清理资源
     */
    void stop()
    {
        if (!isRunning_.exchange(false)) {
            return; // 已经停止
        }
        
        // 通知子类停止运行
        onStop();
        
        // 等待线程结束
        join();
        
        // 清理资源
        free();
    }

protected:
    /**
     * @brief 线程主循环，子类应该重写
     */
    virtual void run() {}

    /**
     * @brief 清理资源，子类应该重写
     */
    virtual void free() {} 
    
    /**
     * @brief 停止通知，子类可重写以实现自定义停止逻辑
     */
    virtual void onStop() {}

    /**
     * @brief 检查线程是否正在运行
     * @return true 正在运行，false 已停止
     */
    bool isRunning() const 
    {
        return isRunning_.load();
    }

private:
    std::thread thread_;                    ///< 工作线程
    std::atomic<bool> isRunning_;          ///< 运行状态标志
};

class MyAvPullStream : public ThreadedTask
{
public:
    MyAvPullStream();
    virtual ~MyAvPullStream();

    // 设置输入文件路径
    void setInputFile(const std::string& filePath);

protected:
    void run() override;
	void free() override;

public:
    bool connect();     // 连接流媒体服务
	bool reConnect();   // 重连流媒体服务
	void closeConnect();// 关闭流媒体服务的连接

	int mConnectCount = 0;

	AVFormatContext* mFmtCtx = NULL;
	// 视频帧
	AVCodecContext* mVideoCodecCtx = NULL;
	AVStream* mVideoStream = NULL;
	bool getVideoPkt(AVPacket& pkt, int& pktQSize);// 从队列获取的pkt，一定要主动释放!!!

	// 音频帧
	AVCodecContext* mAudioCodecCtx = nullptr;
	bool getAudioPkt(AVPacket& pkt, int& pktQSize);// 从队列获取的pkt，一定要主动释放!!!


    bool get_pull_state();
    void set_pull_state(bool bstate);

public:
    //
	static void readThread(void* arg); // 从流媒体服务器（ZLM）拉流
	void handleRead();

    static void decodeVideoThread(void* arg);// 解码实时视频帧
    void handleDecodeVideo();
	
    static void decodeAudioThread(void* arg);// 解码实时音频帧

public:
    int mAudioIndex;
    int mVideoIndex;

    // 视频信息
    int fps = 25;           // 帧率
    int img_w = 0;          // 图像宽度
    int img_h = 0;          // 图像高度
    int img_channel = 3;    // 图像通道数

private:
    // 输入文件路径
    std::string m_inputFilePath;

	bool pushVideoPkt(const AVPacket& pkt);
	void clearVideoPktQueue();
	std::queue <AVPacket>   mVideoPktQ;
	std::mutex              mVideoPktQ_mtx;

	bool pushAudioPkt(const AVPacket& pkt);
	void clearAudioPktQueue();
	std::queue <AVPacket>   mAudioPktQ;
	std::mutex              mAudioPktQ_mtx;

    bool m_pull_state = false;
    std::mutex m_pull_state_mtx;
    std::vector<std::thread*>  m_threads;

};