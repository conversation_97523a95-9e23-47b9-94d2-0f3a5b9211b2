// 音视频拉流解码类实现文件
#include "MyAvPullStream.h"
#include "Common.h"
#include <iostream>
#include <cstdio>

/**
 * @brief 构造函数
 */
MyAvPullStream::MyAvPullStream()
{
    std::cout << "[INFO] MyAvPullStream Start !!!" << std::endl;
    set_pull_state(true);  // 设置拉流状态为启动
}

/**
 * @brief 析构函数
 */
MyAvPullStream::~MyAvPullStream()
{
    std::cout << "[INFO] MyAvPullStream Exit ..." << std::endl;
}

/**
 * @brief 设置输入文件路径
 * @param filePath 本地视频文件路径
 */
void MyAvPullStream::setInputFile(const std::string& filePath)
{
    m_inputFilePath = filePath;
    std::cout << "[INFO] Set input file: " << filePath << std::endl;
}

/**
 * @brief 连接音视频流
 * @return true 连接成功，false 连接失败
 */
bool MyAvPullStream::connect()
{
    // 检查输入文件路径是否已设置
    if (m_inputFilePath.empty()) {
        std::cerr << "[ERROR] Input file path is not set. Please call setInputFile() first." << std::endl;
        return false;
    }

    // 分配格式上下文
    mFmtCtx = avformat_alloc_context();

    // 对于本地文件，不需要设置网络相关参数
    AVDictionary* fmt_options = NULL;
    // 注：本地文件不需要网络参数，以下参数仅用于网络流
    // av_dict_set(&fmt_options, "rtsp_transport", "tcp", 0);   // RTSP网络协议
    // av_dict_set(&fmt_options, "stimeout", "10000000", 0);    // RTSP连接超时
    // av_dict_set(&fmt_options, "rw_timeout", "10000000", 0);  // RTMP/HTTP-FLV连接超时

    // 打开本地视频文件
    int ret = avformat_open_input(&mFmtCtx, m_inputFilePath.c_str(), NULL, &fmt_options);
    if (ret != 0) {
        char error_buf[AV_ERROR_MAX_STRING_SIZE];
        av_strerror(ret, error_buf, AV_ERROR_MAX_STRING_SIZE);
        std::cerr << "[ERROR] avformat_open_input error: file=" << m_inputFilePath
                  << ", error=" << error_buf << std::endl;
        return false;
    }

    std::cout << "[INFO] Successfully opened input file: " << m_inputFilePath << std::endl;

    // 查找流信息
    if (avformat_find_stream_info(mFmtCtx, NULL) < 0) {
        std::cerr << "[ERROR] avformat_find_stream_info error" << std::endl;
        return false;
    }

    // ========== 视频流处理开始 ==========
    // 查找最佳视频流
    mVideoIndex = av_find_best_stream(mFmtCtx, AVMEDIA_TYPE_VIDEO, -1, -1, nullptr, 0);

    /*
    // 旧版本查找视频流的方法（已弃用）
    for (int i = 0; i < mFmtCtx->nb_streams; i++)
    {
        if (mFmtCtx->streams[i]->codec->codec_type == AVMEDIA_TYPE_VIDEO)
        {
            mVideoIndex = i;
            break;
        }
    }
    */

    // 如果找到视频流
    if (mVideoIndex > -1)
    {
        // 获取视频编解码参数
        AVCodecParameters* videoCodecPar = mFmtCtx->streams[mVideoIndex]->codecpar;
        AVCodec* videoCodec = NULL;
        std::cout<<videoCodecPar->codec_id<<std::endl;
        // 如果支持硬件解码
        // 尝试使用硬件解码器（可选）
        bool supportHardwareVideoDecode = true;  // 可以通过参数控制
        if (supportHardwareVideoDecode)
        {
            // 对于H.264编码，尝试使用硬件解码器
            if (AV_CODEC_ID_H264 == videoCodecPar->codec_id)
            {
                if (!videoCodec)
                {
                    videoCodec = const_cast<AVCodec*>(avcodec_find_decoder_by_name("h264_cuvid"));  // NVIDIA独显硬件解码
                    //videoCodec = avcodec_find_decoder_by_name("h264_qsv");                       // Intel核显硬件解码
                    if (videoCodec) {
                        std::cout << "[INFO] Using hardware decoder: h264_cuvid" << std::endl;
                    }
                }
            }else if (AV_CODEC_ID_HEVC== videoCodecPar->codec_id)
            {
                if (!videoCodec)
                {
                    videoCodec = const_cast<AVCodec*>(avcodec_find_decoder_by_name("hevc_cuvid"));  // NVIDIA独显硬件解码
                    //videoCodec = avcodec_find_decoder_by_name("h264_qsv");                       // Intel核显硬件解码
                    if (videoCodec) {
                        std::cout << "[INFO] Using hardware decoder: h264_cuvid" << std::endl;
                    }
                }
            }
            
        }

        // 如果硬件解码器不可用，使用软件解码器
        if (!videoCodec)
        {
            videoCodec = const_cast<AVCodec*>(avcodec_find_decoder(videoCodecPar->codec_id));
            if (!videoCodec)
            {
                std::cerr << "[ERROR] avcodec_find_decoder error" << std::endl;
                return false;
            }
        }

        // 分配解码器上下文
        mVideoCodecCtx = avcodec_alloc_context3(videoCodec);

        // 将编解码参数复制到解码器上下文
        if (avcodec_parameters_to_context(mVideoCodecCtx, videoCodecPar) != 0)
        {
            std::cerr << "[ERROR] avcodec_parameters_to_context error" << std::endl;
            return false;
        }

        // 打开解码器
        if (avcodec_open2(mVideoCodecCtx, videoCodec, nullptr) < 0)
        {
            std::cerr << "[ERROR] avcodec_open2 error" << std::endl;
            return false;
        }
        //mVideoCodecCtx->thread_count = 1;  // 设置解码线程数

        // 获取视频流信息
        mVideoStream = mFmtCtx->streams[mVideoIndex];

        // 计算帧率
        if (0 == mVideoStream->avg_frame_rate.den)
        {
            std::cerr << "[ERROR] videoIndex=" << mVideoIndex
                      << ", videoStream->avg_frame_rate.den = 0" << std::endl;
            fps = 25;  // 默认帧率25fps
        }
        else
        {
            fps = mVideoStream->avg_frame_rate.num / mVideoStream->avg_frame_rate.den;
        }

        // 设置图像参数
        img_w          = mVideoCodecCtx->width;   // 图像宽度
        img_h          = mVideoCodecCtx->height;  // 图像高度
        img_channel    = 3;                       // 图像通道数（BGR为3通道）

        std::cout << "[INFO] Video info: " << img_w << "x" << img_h
                  << ", fps=" << fps << ", channels=" << img_channel << std::endl;

    }
    else {
        std::cerr << "[ERROR] av_find_best_stream video error videoIndex=" << mVideoIndex << std::endl;
        return false;
    }
    // ========== 视频流处理结束 ==========


    // ========== 音频流处理开始 ==========

    // 查找音频流
    mAudioIndex = -1;
    for (unsigned int i = 0; i < mFmtCtx->nb_streams; i++)
    {
        if (mFmtCtx->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_AUDIO)
        {
            mAudioIndex = i;
            break;
        }
    }
    //mControl->audioIndex = av_find_best_stream(mFmtCtx, AVMEDIA_TYPE_AUDIO, -1, -1, nullptr, 0);

    // 如果找到音频流
    if (mAudioIndex > -1)
    {
        // 获取音频编解码参数
        AVCodecParameters* audioCodecPar = mFmtCtx->streams[mAudioIndex]->codecpar;

        // 查找音频解码器
        AVCodec* audiCodec = const_cast<AVCodec*>(avcodec_find_decoder(audioCodecPar->codec_id));
        if (!audiCodec)
        {
            std::cerr << "[ERROR] avcodec_find_decoder error" << std::endl;
            return false;
        }

        // 分配音频解码器上下文
        mAudioCodecCtx = avcodec_alloc_context3(audiCodec);

        // 将编解码参数复制到解码器上下文
        if (avcodec_parameters_to_context(mAudioCodecCtx, audioCodecPar) != 0) {
            std::cerr << "[ERROR] avcodec_parameters_to_context error" << std::endl;
            return false;
        }

        // 打开音频解码器
        if (avcodec_open2(mAudioCodecCtx, audiCodec, nullptr) < 0) {
            std::cerr << "[ERROR] avcodec_open2 error" << std::endl;
            return false;
        }
        //mAudioCodecCtx->thread_count = 1;  // 设置音频解码线程数

    }
    else {
        std::cout << "[WARN] No audio stream found in input file (audioIndex=" << mAudioIndex << ")" << std::endl;
    }
    // ========== 音频流处理结束 ==========

    // TODO: 更新音频索引到数据库


    // 检查是否至少有视频流
    if (mVideoIndex <= -1) {
        return false;
    }

    mConnectCount++;  // 连接计数器递增
    return true;
}

/**
 * @brief 重新连接音视频流
 * @return true 重连成功，false 重连失败
 */
bool MyAvPullStream::reConnect()
{
    // 限制重连次数，避免无限重连
    if (mConnectCount <= 100)
    {
        closeConnect();  // 先关闭当前连接
        if (connect())   // 尝试重新连接
        {
            return true;
        }
        else {
            return false;
        }

    }
    return false;
}

/**
 * @brief 关闭连接并释放资源
 */
void MyAvPullStream::closeConnect()
{
    // 清空数据包队列
    clearVideoPktQueue();
    clearAudioPktQueue();

    // 短暂等待，确保队列操作完成
    std::this_thread::sleep_for(std::chrono::milliseconds(1));

    // 释放视频解码器资源
    if (mVideoCodecCtx)
    {
        avcodec_close(mVideoCodecCtx);              // 关闭解码器
        avcodec_free_context(&mVideoCodecCtx);     // 释放解码器上下文
        mVideoCodecCtx = NULL;
        mVideoIndex = -1;
    }

    // 释放音频解码器资源
    if (mAudioCodecCtx)
    {
        avcodec_close(mAudioCodecCtx);              // 关闭音频解码器
        avcodec_free_context(&mAudioCodecCtx);     // 释放音频解码器上下文
        mAudioCodecCtx = NULL;
        mAudioIndex = -1;
    }

    // 释放格式上下文
    if (mFmtCtx)
    {
        // 注意：拉流场景不需要手动关闭IO（以下代码已注释）
        //if (mFmtCtx && !(mFmtCtx->oformat->flags & AVFMT_NOFILE)) {
        //    avio_close(mFmtCtx->pb);
        //}

        avformat_close_input(&mFmtCtx);    // 关闭输入流
        avformat_free_context(mFmtCtx);    // 释放格式上下文
        mFmtCtx = NULL;
    }
}

/**
 * @brief 将视频数据包推入队列
 * @param pkt 视频数据包
 * @return true 推入成功，false 推入失败
 */
bool MyAvPullStream::pushVideoPkt(const AVPacket& pkt)
{
    // 增加数据包引用计数，确保数据包在队列中的有效性
    if (av_packet_make_refcounted((AVPacket*)&pkt) < 0)
    {
        return false;
    }

    {
        std::lock_guard<std::mutex> lock(mVideoPktQ_mtx);  // 线程安全锁
        std::size_t _size = fps*2;                         // 队列最大长度为帧率的2倍

        // 如果队列已满，移除最旧的数据包
        if(mVideoPktQ.size() >=  _size)
        {
            mVideoPktQ.pop();
        }
        mVideoPktQ.push(pkt);  // 推入新的数据包
    }
    return true;
}

/**
 * @brief 从队列中获取视频数据包
 * @param pkt 输出的视频数据包
 * @param pktQSize 输出当前队列大小
 * @return true 获取成功，false 队列为空
 */
bool MyAvPullStream::getVideoPkt(AVPacket& pkt, int& pktQSize)
{
    bool brst = true;
    {
        std::lock_guard<std::mutex> lock(mVideoPktQ_mtx);  // 线程安全锁
        if (mVideoPktQ.empty())
        {
            brst = false;  // 队列为空
        }else
        {
            pkt = mVideoPktQ.front();       // 获取队列头部数据包
            mVideoPktQ.pop();               // 移除队列头部
            pktQSize = mVideoPktQ.size();   // 返回当前队列大小
        }
    }
    return brst;
}

/**
 * @brief 清空视频数据包队列
 */
void MyAvPullStream::clearVideoPktQueue()
{
    std::lock_guard<std::mutex> lock(mVideoPktQ_mtx);  // 线程安全锁
    while (!mVideoPktQ.empty())
    {
        AVPacket pkt = mVideoPktQ.front();
        mVideoPktQ.pop();
        av_packet_unref(&pkt);  // 释放数据包引用
    }
}

/**
 * @brief 将音频数据包推入队列
 * @param pkt 音频数据包
 * @return true 推入成功，false 推入失败
 */
bool MyAvPullStream::pushAudioPkt(const AVPacket& pkt)
{
    // 增加数据包引用计数，确保数据包在队列中的有效性
    if (av_packet_make_refcounted((AVPacket*)&pkt) < 0)
    {
        return false;
    }

    mAudioPktQ_mtx.lock();      // 加锁保护音频队列
    mAudioPktQ.push(pkt);       // 推入音频数据包
    mAudioPktQ_mtx.unlock();    // 解锁
    return true;
}

/**
 * @brief 从队列中获取音频数据包
 * @param pkt 输出的音频数据包
 * @param pktQSize 输出当前队列大小
 * @return true 获取成功，false 队列为空
 */
bool MyAvPullStream::getAudioPkt(AVPacket& pkt, int& pktQSize)
{
    mAudioPktQ_mtx.lock();  // 加锁保护音频队列
    if (!mAudioPktQ.empty())
    {
        pkt = mAudioPktQ.front();       // 获取队列头部数据包
        mAudioPktQ.pop();               // 移除队列头部
        pktQSize = mAudioPktQ.size();   // 返回当前队列大小
        mAudioPktQ_mtx.unlock();        // 解锁
        return true;
    }
    else {
        mAudioPktQ_mtx.unlock();        // 解锁
        return false;
    }
}

/**
 * @brief 清空音频数据包队列
 */
void MyAvPullStream::clearAudioPktQueue()
{
    mAudioPktQ_mtx.lock();  // 加锁保护音频队列

    while (!mAudioPktQ.empty())
    {
        AVPacket pkt = mAudioPktQ.front();
        mAudioPktQ.pop();
        av_packet_unref(&pkt);  // 释放数据包引用
    }

    mAudioPktQ_mtx.unlock();  // 解锁
}

/**
 * @brief 读取线程入口函数（静态函数）
 * @param arg MyAvPullStream对象指针
 */
void MyAvPullStream::readThread(void* arg)
{
    MyAvPullStream* pullStream  = (MyAvPullStream*)arg;
    pullStream->handleRead();  // 调用实际的读取处理函数
}

/**
 * @brief 处理音视频数据包读取的主循环
 */
void MyAvPullStream::handleRead()
{
    int continuity_error_count = 0;  // 连续错误计数器
    AVPacket pkt;                    // 数据包

    // 主读取循环，直到拉流状态为false
    while (get_pull_state())
    {
        // 从流中读取一个数据包
        if (av_read_frame(this->mFmtCtx, &pkt) >= 0)
        {
            continuity_error_count = 0;  // 读取成功，重置错误计数

            // 根据流索引分发数据包
            if (pkt.stream_index == mVideoIndex)
            {
                pushVideoPkt(pkt);  // 推入视频队列
                std::this_thread::sleep_for(std::chrono::milliseconds(1));  // 短暂休眠
            }
            else if (pkt.stream_index == mAudioIndex)
            {
                 pushAudioPkt(pkt);  // 推入音频队列
            }
            else
            {
                // 其他流类型，直接释放数据包
                //av_free_packet(&pkt);  // 已过时的函数
                av_packet_unref(&pkt);   // 释放数据包引用
            }
        }
        else
        {
            // 读取失败处理
            //av_free_packet(&pkt);  // 已过时的函数
            av_packet_unref(&pkt);   // 释放数据包引用
            continuity_error_count++;

            // 连续错误超过5次（约5秒），尝试重连
            if (continuity_error_count > 5) {
                std::cerr << "[ERROR] av_read_frame error, continuity_error_count = "
                          << continuity_error_count << " (s)" << std::endl;

                if (reConnect())  // 尝试重新连接
                {
                    continuity_error_count = 0;  // 重连成功，重置错误计数
                    std::cout << "[INFO] reConnect success : mConnectCount=" << mConnectCount << std::endl;
                }
                else
                {
                    std::cout << "[INFO] reConnect error : mConnectCount=" << mConnectCount << std::endl;
                    set_pull_state(false);  // 重连失败，停止拉流
                }
            }
            else
            {
                // 等待1秒后重试
                std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            }
        }
    }
}

/**
 * @brief 获取拉流状态（线程安全）
 * @return true 拉流中，false 已停止
 */
bool MyAvPullStream::get_pull_state()
{
    bool mstate = true;
    {
        std::lock_guard<std::mutex> lock(m_pull_state_mtx);  // 线程安全锁
        mstate = m_pull_state;
    }
    return mstate;
}

/**
 * @brief 设置拉流状态（线程安全）
 * @param bstate 新的拉流状态
 */
void MyAvPullStream::set_pull_state(bool bstate)
{
    std::lock_guard<std::mutex> lock(m_pull_state_mtx);  // 线程安全锁
    m_pull_state = bstate;
}

/**
 * @brief 视频解码线程入口函数（静态函数）
 * @param arg MyAvPullStream对象指针
 */
void MyAvPullStream::decodeVideoThread(void* arg)
{
    MyAvPullStream* ps = (MyAvPullStream*)arg;
    ps->handleDecodeVideo();  // 调用实际的视频解码处理函数
}

/**
 * @brief 处理视频解码的主循环
 */
void MyAvPullStream::handleDecodeVideo()
{
    // 获取视频尺寸
    int width  = mVideoCodecCtx->width;
    int height = mVideoCodecCtx->height;

    AVPacket pkt;         // 未解码的视频数据包
    int      pktQSize = 0; // 未解码视频帧队列当前长度

    // 分配帧缓冲区
    AVFrame* frame_yuv420p = av_frame_alloc();  // YUV420P格式帧（解码输出）
    AVFrame* frame_bgr = av_frame_alloc();      // BGR24格式帧（转换输出）

    // 为BGR帧分配内存缓冲区
    int frame_bgr_buff_size = av_image_get_buffer_size(AV_PIX_FMT_BGR24, width, height, 1);
    uint8_t* frame_bgr_buff = (uint8_t*)av_malloc(frame_bgr_buff_size);
    av_image_fill_arrays(frame_bgr->data, frame_bgr->linesize, frame_bgr_buff, AV_PIX_FMT_BGR24, width, height, 1);

    // 创建图像格式转换上下文（YUV420P -> BGR24）
    SwsContext* sws_ctx_yuv420p2bgr = sws_getContext(width, height,
                                                     mVideoCodecCtx->pix_fmt,  // 源格式
                                                     mVideoCodecCtx->width,
                                                     mVideoCodecCtx->height,
                                                     AV_PIX_FMT_BGR24,         // 目标格式
                                                     SWS_BICUBIC, nullptr, nullptr, nullptr);

    // int fps = m_pControl->fps;  // 帧率（备用）

    // ========== 算法检测参数（已注释，备用） ==========
    // bool cur_is_check = false;                    // 当前帧是否进行算法检测
    // int  continuity_check_count = 0;              // 当前连续进行算法检测的帧数
    // int  continuity_check_max_time = 6000;        // 连续进行算法检测允许的最长时间（毫秒）
    // int64_t continuity_check_start = getCurTime(); // 检测开始时间（毫秒）
    // int64_t continuity_check_end = 0;             // 检测结束时间
    // ========== 算法检测参数结束 ==========

    int ret = -1;                    // 函数返回值
    int64_t frameCount = 0;          // 解码帧计数器
    // std::vector<TbPub::InputOriginalData> vector_inputImage;  // 输入图像向量（备用，已注释）

    // 主解码循环，直到拉流状态为false
    while (get_pull_state())
    {
        // 从视频队列获取数据包
        if (getVideoPkt(pkt, pktQSize))
        {
            if (mVideoIndex > -1)  // 确保视频流索引有效
            {
                // 将数据包发送给解码器
                ret = avcodec_send_packet(mVideoCodecCtx, &pkt);
                if (ret == 0)
                {
                    // 从解码器接收解码后的帧
                    ret = avcodec_receive_frame(mVideoCodecCtx, frame_yuv420p);
                    if (ret == 0)
                    {
                        frameCount++;  // 帧计数递增

                        // 图像格式转换：YUV420P -> BGR24
                        sws_scale(sws_ctx_yuv420p2bgr,
                                  frame_yuv420p->data,      // 源数据
                                  frame_yuv420p->linesize,  // 源行大小
                                  0,                        // 源起始行
                                  height,                   // 源高度
                                  frame_bgr->data,          // 目标数据
                                  frame_bgr->linesize);     // 目标行大小

                        // 创建OpenCV Mat对象
                        cv::Mat mImage(height, width, CV_8UC3, frame_bgr->data[0], frame_bgr->linesize[0]);

                        // 这里可以对解码后的图像进行处理
                        // 例如：保存图像、显示图像、进行算法检测等

                        // 示例：每100帧打印一次信息
                        if (frameCount % 100 == 0) {
                            std::cout << "[INFO] Decoded frame " << frameCount
                                      << ", image size: " << width << "x" << height << std::endl;
                        }

                        // 保存图像到文件
                        // 每隔一定帧数保存一张图片，避免保存过多文件
                        if (frameCount % 30 == 0) {  // 每30帧保存一张图片
                            char filename[256];
                            snprintf(filename, sizeof(filename), "output_frame_%06ld.jpg", (long)frameCount);

                            try {
                                bool success = cv::imwrite(filename, mImage);
                                if (success) {
                                    std::cout << "[INFO] Saved frame " << frameCount << " to " << filename << std::endl;
                                } else {
                                    std::cerr << "[ERROR] Failed to save frame " << frameCount << " to " << filename << std::endl;
                                }
                            } catch (const cv::Exception& e) {
                                std::cerr << "[ERROR] OpenCV exception when saving frame: " << e.what() << std::endl;
                            }
                        }

                        // TODO: 在这里添加您的其他图像处理逻辑
                        // 例如：
                        // - 显示图像窗口: cv::imshow("Video", mImage); cv::waitKey(1);
                        // - 进行目标检测
                        // - 图像滤波处理
                        // - 推送到其他模块处理
                    } else if (ret == AVERROR(EAGAIN)) {
                        // 需要更多输入数据，继续
                        continue;
                    } 
                    else {
                        std::cerr << "[ERROR] avcodec_receive_frame error : ret=" << ret << std::endl;
                    }
                }
                else {
                    std::cerr << "[ERROR] avcodec_send_packet error : ret=" << ret << std::endl;
                }
            }

            // 重要：从队列获取的数据包必须释放！
            //av_free_packet(&pkt);  // 已过时的函数
            av_packet_unref(&pkt);   // 释放数据包引用
        }
        else {
            // 队列为空，等待25毫秒
            std::this_thread::sleep_for(std::chrono::milliseconds(25));
        }

        // 控制解码速度，等待25毫秒
        std::this_thread::sleep_for(std::chrono::milliseconds(25));
    }


    // ========== 资源清理 ==========

    // 释放YUV420P帧
    av_frame_free(&frame_yuv420p);
    //av_frame_unref(frame_yuv420p);  // 备用方法
    frame_yuv420p = NULL;

    // 释放BGR帧
    av_frame_free(&frame_bgr);
    //av_frame_unref(frame_bgr);  // 备用方法
    frame_bgr = NULL;

    // 释放BGR缓冲区
    av_free(frame_bgr_buff);
    frame_bgr_buff = NULL;

    // 释放图像转换上下文
    sws_freeContext(sws_ctx_yuv420p2bgr);
    sws_ctx_yuv420p2bgr = NULL;
}

/**
 * @brief 音频解码线程入口函数（静态函数）
 * @param arg MyAvPullStream对象指针
 * @note 当前版本暂时不使用音频解码功能，代码已注释
 */
void MyAvPullStream::decodeAudioThread(void* arg)
{
    // 音频解码功能暂时未启用，以下为完整的音频解码实现（已注释）

    // ControlExecutor* executor = (ControlExecutor*)arg;
    // AVPacket pkt;         // 未解码的音频数据包
    // int      pktQSize = 0; // 未解码音频帧队列当前长度
    // AVFrame* frame = av_frame_alloc();  // 解码后的音频帧

    // ========== 音频输入参数 ==========
    // int in_channels = executor->mPullStream->mAudioCodecCtx->channels;                    // 输入声道数
    // uint64_t in_channel_layout = av_get_default_channel_layout(in_channels);              // 输入声道布局
    // AVSampleFormat in_sample_fmt = executor->mPullStream->mAudioCodecCtx->sample_fmt;     // 输入采样格式
    // int in_sample_rate = executor->mPullStream->mAudioCodecCtx->sample_rate;              // 输入采样率
    // int in_nb_samples = executor->mPullStream->mAudioCodecCtx->frame_size;                // 输入每帧采样数

    // ========== 音频重采样输出参数 ==========
    // uint64_t out_channel_layout = AV_CH_LAYOUT_STEREO;                                    // 输出声道布局（立体声）
    // int out_channels = av_get_channel_layout_nb_channels(out_channel_layout);             // 输出声道数
    // AVSampleFormat out_sample_fmt = AV_SAMPLE_FMT_S16;  // 输出采样格式（16位整型）
    //                                                      // 注：FFmpeg对AAC编码默认支持AV_SAMPLE_FMT_FLTP
    //                                                      //     PCM文件或播放器通常使用AV_SAMPLE_FMT_S16
    // int out_sample_rate = 44100;                        // 输出采样率（44.1kHz）
    // int out_nb_samples = 1024;                          // 输出每帧单个通道的采样点数

    // ========== 创建音频重采样上下文 ==========
    // struct SwrContext* swr_ctx_audioConvert = swr_alloc();
    // swr_ctx_audioConvert = swr_alloc_set_opts(swr_ctx_audioConvert,
    //     out_channel_layout,   // 输出声道布局
    //     out_sample_fmt,       // 输出采样格式
    //     out_sample_rate,      // 输出采样率
    //     in_channel_layout,    // 输入声道布局
    //     in_sample_fmt,        // 输入采样格式
    //     in_sample_rate,       // 输入采样率
    //     0, NULL);
    // swr_init(swr_ctx_audioConvert);  // 初始化重采样上下文


    // ========== 分配输出缓冲区 ==========
    // int out_buff_size = av_samples_get_buffer_size(NULL, out_channels, out_nb_samples, out_sample_fmt, 1);
    // uint8_t* out_buff = (uint8_t*)av_malloc(out_buff_size);  // 重采样得到的PCM数据缓冲区

    // ========== 音频解码主循环 ==========
    // int ret = -1;
    // int64_t frameCount = 0;    // 音频帧计数器
    // int64_t t3, t4 = 0;        // 性能计时变量
    // while (executor->get_pull_state())
    // {
    //     // 从音频队列获取数据包
    //     if (executor->mPullStream->getAudioPkt(pkt, pktQSize)) {
    //         if (executor->mPullStream->mAudioIndex > -1) {
    //             t3 = Analyzer_getCurTime();  // 记录解码开始时间
    //
    //             // 将数据包发送给音频解码器
    //             ret = avcodec_send_packet(executor->mPullStream->mAudioCodecCtx, &pkt);
    //             if (ret == 0) {
    //                 // 从解码器接收解码后的音频帧
    //                 while (avcodec_receive_frame(executor->mPullStream->mAudioCodecCtx, frame) == 0) {
    //                     t4 = Analyzer_getCurTime();  // 记录解码结束时间
    //                     frameCount++;
    //
    //                     // 音频重采样：将解码后的音频转换为目标格式
    //                     swr_convert(swr_ctx_audioConvert, &out_buff, out_buff_size,
    //                                (const uint8_t**)frame->data, frame->nb_samples);
    //
    //                     // 性能日志（已注释）
    //                     //LOGI("decode 1 frame frameCount=%lld,decode spend：%lld(ms),pktQSize=%d",
    //                     //    frameCount, (t4 - t3), pktQSize);
    //
    //                     // 如果有推流对象，将重采样后的音频数据推送出去
    //                     if (executor->mPushStream != nullptr)
    //                     {
    //                         // 注：重采样参数决定一帧音频数据大小为out_buff_size=4096字节
    //                         executor->mPushStream->pushAudioFrame(out_buff, out_buff_size);
    //                     }
    //                 }
    //             }
    //             else {
    //                 LOGE("avcodec_send_packet : ret=%d", ret);
    //             }
    //         }
    //
    //         // 重要：从队列获取的数据包必须释放！
    //         //av_free_packet(&pkt);  // 已过时的函数
    //         av_packet_unref(&pkt);   // 释放数据包引用
    //     }
    //     else {
    //         // 队列为空，等待5毫秒
    //         std::this_thread::sleep_for(std::chrono::milliseconds(5));
    //     }
    // }

    // ========== 音频解码资源清理 ==========
    // av_frame_free(&frame);      // 释放音频帧
    // //av_frame_unref(frame);     // 备用方法
    // frame = NULL;

    // av_free(out_buff);          // 释放输出缓冲区
    // out_buff = NULL;

    // swr_free(&swr_ctx_audioConvert);  // 释放重采样上下文
    // swr_ctx_audioConvert = NULL;
}

/**
 * @brief 启动拉流和解码线程
 */
void MyAvPullStream::run()
{
    set_pull_state(true);  // 将执行状态设置为true

    if (connect())  // 尝试连接音视频流
    {
        // 创建数据包读取线程
        std::thread* th = new std::thread(readThread, this);
        m_threads.push_back(th);

        // 创建视频解码线程
        th = new std::thread(decodeVideoThread, this);
        m_threads.push_back(th);

        // 音频解码线程（暂时不启用）
        //th = new std::thread(ControlExecutor::decodeAudioThread, this);
        //mPullThreads.push_back(th);
    }
    else
    {
        // 连接失败的日志处理
        std::cerr << "[ERROR] Failed to connect to stream" << std::endl;
    }
}

/**
 * @brief 释放资源并停止所有线程
 */
void MyAvPullStream::free()
{
    set_pull_state(false);  // 设置拉流状态为停止

    // 等待100毫秒，让线程有时间响应状态变化
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    closeConnect();  // 关闭连接并释放FFmpeg资源
    stop();          // 停止相关操作
    join();          // 等待相关操作完成

    // 等待所有线程完成并清理线程资源
    for (auto& thread : m_threads)
    {
        if (thread->joinable())  // 检查线程是否可以join
        {
            thread->join();      // 等待线程结束
        }
    }
    m_threads.clear();  // 清空线程容器
}


